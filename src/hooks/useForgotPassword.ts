import {useMutation} from '@tanstack/react-query';
import {AuthenticationInstance} from '@/services/BackendServices';

interface ForgotPasswordRequest {
  email: string;
}

interface ForgotPasswordResponse {
  message: string;
  success: boolean;
}

interface ForgotPasswordError {
  message: string;
  status?: number;
}

/**
 * Custom hook for forgot password functionality
 * Uses TanStack Query for API state management with proper error handling and retry logic
 */
export const useForgotPasswordMutation = () => {
  return useMutation<ForgotPasswordResponse, ForgotPasswordError, ForgotPasswordRequest>({
    mutationFn: async ({email}: ForgotPasswordRequest) => {
      try {
        const {data} = await AuthenticationInstance.post<ForgotPasswordResponse>(
          '/user/reset-password',
          {
            email: email.trim().toLowerCase(),
          },
        );
        return data;
      } catch (error: any) {
        // Handle different error types
        if (error.response?.data?.message) {
          throw {
            message: error.response.data.message,
            status: error.response.status,
          };
        } else if (error.message) {
          throw {
            message: error.message,
            status: error.response?.status,
          };
        } else {
          throw {
            message: 'Network error. Please check your connection and try again.',
            status: 0,
          };
        }
      }
    },
    retry: (failureCount, error) => {
      // Retry logic: retry up to 2 times for network errors, but not for client errors
      if (error.status && error.status >= 400 && error.status < 500) {
        return false; // Don't retry client errors (4xx)
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

/**
 * Email validation utility using the same pattern as the existing codebase
 * Based on the regex from subscribeScreenUtils.ts
 */
export const validateEmail = (email: string): boolean => {
  const validEmailRegex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/;
  return validEmailRegex.test(email.trim());
};

/**
 * Comprehensive email validation with detailed error messages
 */
export const validateEmailWithMessage = (
  email: string,
): {isValid: boolean; message?: string} => {
  const trimmedEmail = email.trim();

  if (!trimmedEmail) {
    return {isValid: false, message: 'Email address is required'};
  }

  if (!validateEmail(trimmedEmail)) {
    return {isValid: false, message: 'Please enter a valid email address'};
  }

  return {isValid: true};
};
