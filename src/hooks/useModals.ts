import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {useRef} from 'react';

/**
 * Hook to manage multiple modal refs
 */
export const useModals = <T extends string>(modalNames: T[]) => {
  const refs = modalNames.reduce((acc, name) => {
    console.log('acc', acc);
    console.log('name', name);
    acc[name] = useRef<BottomSheetModal>(null);
    return acc;
  }, {} as Record<T, React.RefObject<BottomSheetModal>>);

  const show = (modalName: T) => {
    refs[modalName].current?.present();
  };

  const hide = (modalName: T) => {
    refs[modalName].current?.dismiss();
  };

  const hideAll = () => {
    Object.values(refs).forEach((ref) => {
      (ref as React.RefObject<BottomSheetModal>).current?.dismiss();
    });
  };

  return {refs, show, hide, hideAll};
};
