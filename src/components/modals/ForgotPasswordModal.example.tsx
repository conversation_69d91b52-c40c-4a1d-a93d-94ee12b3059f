/**
 * Example usage of ForgotPasswordModal component
 * 
 * This file demonstrates how to integrate the ForgotPasswordModal
 * into your authentication screens following the project's patterns.
 */

import React, {useRef} from 'react';
import {TouchableOpacity, Text, View, StyleSheet} from 'react-native';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import ForgotPasswordModal from './ForgotPasswordModal';
import GlobalStyles from '@/constants/GlobalStyles';

/**
 * Example 1: Basic usage in a login screen
 */
export const LoginScreenExample: React.FC = () => {
  const forgotPasswordModalRef = useRef<BottomSheetModal>(null);

  const handleForgotPasswordPress = () => {
    forgotPasswordModalRef.current?.present();
  };

  const handleModalClose = () => {
    // Optional: Handle any cleanup when modal closes
    console.log('Forgot password modal closed');
  };

  return (
    <View style={styles.container}>
      {/* Your login form components here */}
      
      {/* Forgot Password Link */}
      <TouchableOpacity onPress={handleForgotPasswordPress}>
        <Text style={styles.forgotPasswordLink}>Forgot Password?</Text>
      </TouchableOpacity>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal 
        ref={forgotPasswordModalRef} 
        onClose={handleModalClose}
      />
    </View>
  );
};

/**
 * Example 2: Using with useModals hook (project pattern)
 */
export const LoginScreenWithUseModals: React.FC = () => {
  const forgotPasswordModalRef = useRef<BottomSheetModal>(null);

  // Alternative approach using the project's useModals pattern
  // You could extend useModals to include 'forgotPassword' modal type
  
  const handleForgotPasswordPress = () => {
    forgotPasswordModalRef.current?.present();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleForgotPasswordPress}>
        <Text style={styles.forgotPasswordLink}>Forgot Password?</Text>
      </TouchableOpacity>

      <ForgotPasswordModal ref={forgotPasswordModalRef} />
    </View>
  );
};

/**
 * Example 3: Integration with existing LoanLogin screen
 * 
 * To integrate into src/screens/Loan/screens/LoanLogin.tsx:
 * 
 * 1. Add import:
 *    import ForgotPasswordModal from '@/components/modals/ForgotPasswordModal';
 * 
 * 2. Add ref:
 *    const forgotPasswordModalRef = useRef<BottomSheetModal>(null);
 * 
 * 3. Replace the incomplete TouchableOpacity (line 131):
 *    <TouchableOpacity onPress={() => forgotPasswordModalRef.current?.present()}>
 *      <Text style={styles.signUpLink}>Forgot Password?</Text>
 *    </TouchableOpacity>
 * 
 * 4. Add modal component before closing View:
 *    <ForgotPasswordModal ref={forgotPasswordModalRef} />
 */

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  forgotPasswordLink: {
    color: GlobalStyles.primary.primary400,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    textDecorationLine: 'underline',
  },
});

/**
 * API Endpoint Requirements:
 * 
 * The ForgotPasswordModal expects a POST endpoint at:
 * /user/forgot-password
 * 
 * Request body:
 * {
 *   "email": "<EMAIL>"
 * }
 * 
 * Expected response format:
 * Success (200):
 * {
 *   "message": "Password reset link sent successfully",
 *   "success": true
 * }
 * 
 * Error (400/404/500):
 * {
 *   "message": "User not found" // or other error message
 * }
 */

/**
 * Features included in ForgotPasswordModal:
 * 
 * ✅ Email validation with real-time feedback
 * ✅ Form submission with loading states
 * ✅ Success toast with custom message including email
 * ✅ Error handling with appropriate error messages
 * ✅ Network resilience with retry logic
 * ✅ Proper TypeScript typing (no any types)
 * ✅ Follows project's styling patterns and design tokens
 * ✅ Uses existing UI components (TextInput, MButton, BaseModal)
 * ✅ TanStack Query integration for API calls
 * ✅ Accessibility compliance
 * ✅ Modal dismiss functionality
 * ✅ Matches the provided design screenshots
 */
