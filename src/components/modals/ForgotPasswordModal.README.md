# ForgotPasswordModal Component

A production-ready React Native modal component for password reset functionality that follows the project's architecture patterns and design system.

## Features

✅ **Email validation** with real-time feedback using regex pattern  
✅ **Form submission** with loading states and proper error handling  
✅ **Custom success toast** with email icon matching the provided design  
✅ **Network resilience** with retry logic and timeout handling  
✅ **TypeScript support** with strict typing (no `any` types)  
✅ **Accessibility compliance** with proper labels and focus management  
✅ **Responsive design** that works across different screen sizes  
✅ **Security-focused** with email sanitization and validation  

## Design Implementation

The component matches the provided screenshots exactly:

- **Modal Structure**: Uses BaseModal with "Reset Password" title
- **Description Text**: Matches the exact wording from the design
- **Email Input**: Uses project's TextInput component with proper styling
- **Submit Button**: Primary button with loading states and proper disabled states
- **Success Toast**: Custom toast with black circular background, white envelope icon, and structured message layout

## Usage

### Basic Implementation

```tsx
import React, {useRef} from 'react';
import {TouchableOpacity, Text} from 'react-native';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import ForgotPasswordModal from '@/components/modals/ForgotPasswordModal';

const LoginScreen: React.FC = () => {
  const forgotPasswordModalRef = useRef<BottomSheetModal>(null);

  const handleForgotPasswordPress = () => {
    forgotPasswordModalRef.current?.present();
  };

  return (
    <>
      <TouchableOpacity onPress={handleForgotPasswordPress}>
        <Text>Forgot Password?</Text>
      </TouchableOpacity>

      <ForgotPasswordModal ref={forgotPasswordModalRef} />
    </>
  );
};
```

### With onClose Callback

```tsx
<ForgotPasswordModal 
  ref={forgotPasswordModalRef} 
  onClose={() => console.log('Modal closed')}
/>
```

## API Integration

### Required Backend Endpoint

The component expects a POST endpoint at `/user/forgot-password`:

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "message": "Password reset link sent successfully",
  "success": true
}
```

**Error Response (400/404/500):**
```json
{
  "message": "User not found"
}
```

### Retry Logic

- **Network errors**: Retries up to 2 times with exponential backoff
- **Client errors (4xx)**: No retry (immediate failure)
- **Server errors (5xx)**: Retries with backoff

## Architecture

### Components Used

- **BaseModal**: Project's standard modal wrapper
- **TextInput**: Project's form input component with validation
- **MButton**: Project's button component with loading states
- **BodyM**: Project's typography component

### Hooks Used

- **useForgotPasswordMutation**: Custom TanStack Query hook for API calls
- **validateEmailWithMessage**: Email validation utility

### State Management

- **Email input**: Local state with real-time validation
- **Form validation**: Computed validation state
- **API state**: Managed by TanStack Query (loading, error, success)

## Styling

### Design Tokens

- Uses `GlobalStyles` for colors and spacing
- Uses `theme.spacing` for consistent spacing
- Follows dark mode minimalist design principles

### Color Scheme

- **Background**: `GlobalStyles.base.white`
- **Text**: `GlobalStyles.base.black` and `GlobalStyles.gray.gray900`
- **Primary Button**: `GlobalStyles.primary.primary400`
- **Toast Icon Background**: `GlobalStyles.base.black`

## Validation

### Email Validation Rules

- **Required**: Email field cannot be empty
- **Format**: Must match regex `/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/`
- **Sanitization**: Automatically trims whitespace and converts to lowercase

### Form Validation

- Real-time validation with immediate feedback
- Button disabled until valid email is entered
- Clear error messages for invalid inputs

## Error Handling

### Client-Side Errors

- Empty email field
- Invalid email format
- Network connectivity issues

### Server-Side Errors

- User not found (404)
- Server errors (500)
- Rate limiting (429)

### Error Display

- Form validation errors shown below input field
- API errors shown as warning toasts
- Network errors handled gracefully with retry logic

## Success Flow

1. **Form Submission**: Email validated and API called
2. **Success Response**: Custom toast displayed with email icon
3. **Modal Dismissal**: Modal automatically closes
4. **Callback Execution**: Optional onClose callback triggered

## Testing

### Manual Testing Checklist

- [ ] Modal opens and closes correctly
- [ ] Email validation works for valid/invalid emails
- [ ] Submit button disabled for invalid emails
- [ ] Loading state shows during API call
- [ ] Success toast appears with correct styling
- [ ] Error handling works for network failures
- [ ] Modal dismisses after successful submission

### Integration Points

- Ensure backend endpoint `/user/forgot-password` is implemented
- Verify email service is configured for sending reset links
- Test with various email formats and edge cases

## Dependencies

- `@gorhom/bottom-sheet`: Modal functionality
- `@tanstack/react-query`: API state management
- `react-native-flash-message`: Toast notifications
- `react-native-heroicons`: Email icon
- Project's existing UI components and utilities

## Security Considerations

- Email addresses are sanitized (trimmed and lowercased)
- No sensitive data stored in component state
- API errors don't expose sensitive information
- Rate limiting should be implemented on backend
- HTTPS required for production API calls
